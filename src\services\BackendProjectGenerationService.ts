import { io, Socket } from 'socket.io-client';
import { 
  ProjectContext, 
  Task, 
  AgentLog, 
  DecisionLogEntry, 
  LicenseInfo, 
  AgentType, 
  UserFeedback, 
  FileNode 
} from '../types';

/**
 * Service that communicates with the backend for project generation
 * This replaces the frontend agents that were causing memory issues
 */
export class BackendProjectGenerationService {
  private socket: Socket | null = null;
  private baseUrl: string;
  private isInitialized = false;

  // Event handlers
  private onCompanyLogHandler?: (log: AgentLog) => void;
  private onTaskLogHandler?: (log: any) => void;
  private onDecisionLogHandler?: (entry: DecisionLogEntry) => void;
  private onProjectGenerationUpdateHandler?: (update: any) => void;
  private onProjectGenerationErrorHandler?: (error: any) => void;

  constructor(baseUrl: string = 'http://localhost:3002') {
    this.baseUrl = baseUrl;
  }

  /**
   * Initialize the service and connect to WebSocket
   */
  public async initialize(): Promise<void> {
    if (this.isInitialized) {
      return;
    }

    // Connect to WebSocket
    this.socket = io(this.baseUrl);

    this.socket.on('connect', () => {
      console.log('[BackendProjectGenerationService] Connected to backend WebSocket');
    });

    this.socket.on('disconnect', () => {
      console.log('[BackendProjectGenerationService] Disconnected from backend WebSocket');
    });

    this.socket.on('company-log', (log: AgentLog) => {
      if (this.onCompanyLogHandler) {
        this.onCompanyLogHandler(log);
      }
    });

    this.socket.on('task-log', (log: any) => {
      if (this.onTaskLogHandler) {
        this.onTaskLogHandler(log);
      }
    });

    this.socket.on('decision-log', (entry: DecisionLogEntry) => {
      if (this.onDecisionLogHandler) {
        this.onDecisionLogHandler(entry);
      }
    });

    this.socket.on('project-generation-update', (update: any) => {
      if (this.onProjectGenerationUpdateHandler) {
        this.onProjectGenerationUpdateHandler(update);
      }
    });

    this.socket.on('project-generation-error', (error: any) => {
      if (this.onProjectGenerationErrorHandler) {
        this.onProjectGenerationErrorHandler(error);
      }
    });

    this.isInitialized = true;
  }

  /**
   * Disconnect from the backend
   */
  public disconnect(): void {
    if (this.socket) {
      this.socket.disconnect();
      this.socket = null;
    }
    this.isInitialized = false;
  }

  /**
   * Initialize the backend service with API key
   */
  public async initializeBackendWithApiKey(apiKey: string): Promise<void> {
    const response = await fetch(`${this.baseUrl}/api/generation/initialize`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ apiKey }),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.error || 'Failed to initialize backend service');
    }

    console.log('[BackendProjectGenerationService] Backend initialized with API key');
  }

  /**
   * Start project generation on the backend
   */
  public async startProjectGeneration(
    projectId: string,
    projectIdea: string,
    licenseInfo: LicenseInfo,
    agentModelConfiguration: Record<AgentType, string>
  ): Promise<void> {
    // Join the project room for real-time updates
    if (this.socket) {
      this.socket.emit('join-project', projectId);
    }

    const response = await fetch(`${this.baseUrl}/api/generation/start`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        projectId,
        projectIdea,
        licenseInfo,
        agentModelConfiguration,
      }),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.error || 'Failed to start project generation');
    }

    console.log('[BackendProjectGenerationService] Project generation started');
  }

  /**
   * Get project generation status
   */
  public async getGenerationStatus(): Promise<{ isGenerating: boolean; projectId: string | null }> {
    const response = await fetch(`${this.baseUrl}/api/generation/status`);

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.error || 'Failed to get generation status');
    }

    return response.json();
  }

  /**
   * Process user feedback and generate additional tasks
   */
  public async processUserFeedback(
    projectId: string,
    projectContext: string,
    projectIdea: string,
    fileStructure: FileNode[],
    feedback: UserFeedback,
    modelName: string
  ): Promise<Task[]> {
    const response = await fetch(`${this.baseUrl}/api/generation/feedback`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        projectId,
        projectContext,
        projectIdea,
        fileStructure,
        feedback,
        modelName,
      }),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.error || 'Failed to process user feedback');
    }

    const result = await response.json();
    return result.tasks;
  }

  /**
   * Set event handlers for real-time updates
   */
  public setEventHandlers(handlers: {
    onCompanyLog?: (log: AgentLog) => void;
    onTaskLog?: (log: any) => void;
    onDecisionLog?: (entry: DecisionLogEntry) => void;
    onProjectGenerationUpdate?: (update: any) => void;
    onProjectGenerationError?: (error: any) => void;
  }): void {
    this.onCompanyLogHandler = handlers.onCompanyLog;
    this.onTaskLogHandler = handlers.onTaskLog;
    this.onDecisionLogHandler = handlers.onDecisionLog;
    this.onProjectGenerationUpdateHandler = handlers.onProjectGenerationUpdate;
    this.onProjectGenerationErrorHandler = handlers.onProjectGenerationError;
  }

  /**
   * Check if the service is initialized
   */
  public get initialized(): boolean {
    return this.isInitialized;
  }

  /**
   * Check if WebSocket is connected
   */
  public get connected(): boolean {
    return this.socket?.connected || false;
  }
}
